-- liquibase formatted sql

-- changeset redmi:1755871212748-67 contextFilter:"main" splitStatements:false
ALTER TABLE message_details DROP FOREIGN KEY FK_Message_Details_Notification_Organization_Id;

-- changeset redmi:1755871212748-68 contextFilter:"main" splitStatements:false
ALTER TABLE message_queue DROP FOREIGN KEY FK_Message_Queue_Message_Details_MessageDetailsId;

-- changeset redmi:1755871212748-69 contextFilter:"main" splitStatements:false
ALTER TABLE notification DROP FOREIGN KEY FK_Notification_NotificationType_NotificationTypeId;

-- changeset redmi:1755871212748-70 contextFilter:"main" splitStatements:false
ALTER TABLE notification DROP FOREIGN KEY FK_Notification_Organization_OrganizationId;

-- changeset redmi:1755871212748-71 contextFilter:"main" splitStatements:false
ALTER TABLE notification_organizations DROP FOREIGN KEY FK_Notification_Organizations_Notification_NotificationId;

-- changeset redmi:1755871212748-72 contextFilter:"main" splitStatements:false
ALTER TABLE notification_organizations DROP FOREIGN KEY FK_Notification_Organizations_Organization_OrganizationId;

-- changeset redmi:1755871212748-73 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscribers DROP FOREIGN KEY FK_Notification_Subscribers_Notification_Organization_Id;

-- changeset redmi:1755871212748-74 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscribers DROP FOREIGN KEY FK_Notification_Subscribers_Subscriber_SubscriberId;

-- changeset redmi:1755871212748-75 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscription DROP FOREIGN KEY FK_Notification_Subscription_Service_Organization;

-- changeset redmi:1755871212748-76 contextFilter:"main" splitStatements:false
ALTER TABLE notification_subscription DROP FOREIGN KEY FK_Notification_Subscription_User;

-- changeset redmi:1755871212748-77 contextFilter:"main" splitStatements:false
ALTER TABLE notification DROP FOREIGN KEY FK_Notification_User_UserId;

-- changeset redmi:1755871212748-78 contextFilter:"main" splitStatements:false
ALTER TABLE policy DROP FOREIGN KEY FK_Policy_CreatedBy_User_UserId;

-- changeset redmi:1755871212748-79 contextFilter:"main" splitStatements:false
ALTER TABLE policy DROP FOREIGN KEY FK_Policy_ScheduleId_Schedule_ScheduleId;

-- changeset redmi:1755871212748-80 contextFilter:"main" splitStatements:false
ALTER TABLE policy DROP FOREIGN KEY FK_Policy_UpdatedBy_User_UserId;

-- changeset redmi:1755871212748-81 contextFilter:"main" splitStatements:false
ALTER TABLE `role` DROP FOREIGN KEY FK_Role_OrganizationId_Organization_OrganizationId;

-- changeset redmi:1755871212748-82 contextFilter:"main" splitStatements:false
ALTER TABLE service_configurations DROP FOREIGN KEY FK_Service_Configurations_Service_ServiceId;

-- changeset redmi:1755871212748-83 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization DROP FOREIGN KEY FK_User_Organization_Organization_OrganizationId;

-- changeset redmi:1755871212748-84 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization DROP FOREIGN KEY FK_User_Organization_Service_UserId;

-- changeset redmi:1755871212748-85 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder DROP FOREIGN KEY FK_device_folder_PolicyId_Policy_PolicyId;

-- changeset redmi:1755871212748-86 contextFilter:"main" splitStatements:false
ALTER TABLE services_organizations DROP FOREIGN KEY FK_services_organizations_Organization_OrganizationId;

-- changeset redmi:1755871212748-87 contextFilter:"main" splitStatements:false
ALTER TABLE services_organizations DROP FOREIGN KEY FK_services_organizations_Service_ServiceId;

-- changeset redmi:1755871212748-88 contextFilter:"main" splitStatements:false
ALTER TABLE device_alert DROP FOREIGN KEY fk_device_alert_policy_device_alert;

-- changeset redmi:1755871212748-89 contextFilter:"main" splitStatements:false
ALTER TABLE device_alert DROP FOREIGN KEY fk_device_alert_user;

-- changeset redmi:1755871212748-90 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert DROP FOREIGN KEY fk_policy_device_alert_policy_device_alert_type;

-- changeset redmi:1755871212748-91 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert DROP FOREIGN KEY fk_policy_device_alert_policy_policy_id;

-- changeset redmi:1755871212748-92 contextFilter:"main" splitStatements:false
ALTER TABLE schedule DROP FOREIGN KEY schedule_ibfk_1;

-- changeset redmi:1755871212748-93 contextFilter:"main" splitStatements:false
ALTER TABLE schedule DROP FOREIGN KEY schedule_ibfk_2;

-- changeset redmi:1755871212748-94 contextFilter:"main" splitStatements:false
ALTER TABLE schedule DROP FOREIGN KEY schedule_ibfk_3;

-- changeset redmi:1755871212748-95 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization_mapping DROP FOREIGN KEY user_organization_mapping_ibfk_1;

-- changeset redmi:1755871212748-59 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD UserId INT DEFAULT null NULL;

-- changeset redmi:1755871212748-60 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD OrganizationId INT DEFAULT null NULL;

-- changeset redmi:1755871212748-61 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD IsActive BIT DEFAULT 0 NOT NULL;

-- changeset redmi:1755871212748-62 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD IsDefault BIT DEFAULT 0 NOT NULL;

-- changeset redmi:1755871212748-63 contextFilter:"main" splitStatements:false
CREATE INDEX IX_User_Organization_Mapping_OrganizationId ON User_Organization_Mapping(OrganizationId);

-- changeset redmi:1755871212748-64 contextFilter:"main" splitStatements:false
CREATE INDEX IX_User_Organization_Mapping_UserId ON User_Organization_Mapping(UserId);

-- changeset redmi:1755871212748-65 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_Organization_OrganizationId FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:1755871212748-66 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_User_UserId FOREIGN KEY (UserId) REFERENCES User (UserId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:1755871212748-96 contextFilter:"main" splitStatements:false
ALTER TABLE configuration DROP KEY Category;

-- changeset redmi:1755871212748-97 contextFilter:"main" splitStatements:false
ALTER TABLE user DROP KEY Email;

-- changeset redmi:1755871212748-98 contextFilter:"main" splitStatements:false
ALTER TABLE configurationvalues DROP KEY Name_ConfigurationId;

-- changeset redmi:1755871212748-99 contextFilter:"main" splitStatements:false
ALTER TABLE configuration_value_organization DROP KEY OrganizationId_ConfigurationValuesId;

-- changeset redmi:1755871212748-100 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization DROP KEY UserId_OrganizationId;

-- changeset redmi:1755871212748-101 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert DROP KEY uq_policy_device_alert_type_id_policy_id;

-- changeset redmi:1755871212748-102 contextFilter:"main" splitStatements:false
ALTER TABLE device_alert DROP KEY ux_device_alert_pda_id_a_id_created_on_resource_instance;

-- changeset redmi:1755871212748-103 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert_type DROP KEY ux_name;

-- changeset redmi:1755871212748-104 contextFilter:"main" splitStatements:false
ALTER TABLE policy_device_alert_type DROP KEY ux_type_id;

-- changeset redmi:1755871212748-105 contextFilter:"main" splitStatements:false
DROP TABLE configuration_value_organization;

-- changeset redmi:1755871212748-106 contextFilter:"main" splitStatements:false
DROP TABLE device_alert;

-- changeset redmi:1755871212748-107 contextFilter:"main" splitStatements:false
DROP TABLE message_details;

-- changeset redmi:1755871212748-108 contextFilter:"main" splitStatements:false
DROP TABLE message_queue;

-- changeset redmi:1755871212748-109 contextFilter:"main" splitStatements:false
DROP TABLE notification;

-- changeset redmi:1755871212748-110 contextFilter:"main" splitStatements:false
DROP TABLE notification_organizations;

-- changeset redmi:1755871212748-111 contextFilter:"main" splitStatements:false
DROP TABLE notification_subscribers;

-- changeset redmi:1755871212748-112 contextFilter:"main" splitStatements:false
DROP TABLE notification_subscription;

-- changeset redmi:1755871212748-113 contextFilter:"main" splitStatements:false
DROP TABLE notification_type;

-- changeset redmi:1755871212748-114 contextFilter:"main" splitStatements:false
DROP TABLE policy;

-- changeset redmi:1755871212748-115 contextFilter:"main" splitStatements:false
DROP TABLE policy_device_alert;

-- changeset redmi:1755871212748-116 contextFilter:"main" splitStatements:false
DROP TABLE policy_device_alert_type;

-- changeset redmi:1755871212748-117 contextFilter:"main" splitStatements:false
DROP TABLE regex_filter;

-- changeset redmi:1755871212748-118 contextFilter:"main" splitStatements:false
DROP TABLE schedule;

-- changeset redmi:1755871212748-119 contextFilter:"main" splitStatements:false
DROP TABLE service;

-- changeset redmi:1755871212748-120 contextFilter:"main" splitStatements:false
DROP TABLE service_configurations;

-- changeset redmi:1755871212748-121 contextFilter:"main" splitStatements:false
DROP TABLE services_organizations;

-- changeset redmi:1755871212748-122 contextFilter:"main" splitStatements:false
DROP TABLE user_organization;

-- changeset redmi:1755871212748-123 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder DROP COLUMN `Description`;

-- changeset redmi:1755871212748-124 contextFilter:"main" splitStatements:false
ALTER TABLE configuration DROP COLUMN IsUserVisible;

-- changeset redmi:1755871212748-125 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder DROP COLUMN PolicyId;

-- changeset redmi:1755871212748-126 contextFilter:"main" splitStatements:false
ALTER TABLE user_organization_mapping DROP COLUMN UserOrganizationId;

-- changeset redmi:1755871212748-127 contextFilter:"main" splitStatements:false
ALTER TABLE user DROP COLUMN is_api_user;

-- changeset redmi:1755871212748-128 contextFilter:"main" splitStatements:false
DROP INDEX UserOrganizationId_RoleId ON user_organization_mapping;

-- changeset redmi:1755871212748-1 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER AllowSubOrg SET DEFAULT 0;

-- changeset redmi:1755871212748-2 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER AllowWhiteLabel SET DEFAULT 0;

-- changeset redmi:1755871212748-3 contextFilter:"main" splitStatements:false
ALTER TABLE OrganizationMapping MODIFY Application ENUM('CloudInfra', 'ConnectWise');

-- changeset redmi:1755871212748-4 contextFilter:"main" splitStatements:false
ALTER TABLE OrganizationMapping MODIFY Application ENUM('CloudInfra', 'ConnectWise') NOT NULL;

-- changeset redmi:1755871212748-5 contextFilter:"main" splitStatements:false
ALTER TABLE OrganizationMapping ALTER Application DROP DEFAULT;

-- changeset redmi:1755871212748-6 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY AvailableTo INT NULL;

-- changeset redmi:1755871212748-7 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER AvailableTo SET DEFAULT (NULL);

-- changeset redmi:1755871212748-8 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY CreatedBy INT NULL;

-- changeset redmi:1755871212748-9 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ALTER CreatedBy SET DEFAULT (NULL);

-- changeset redmi:1755871212748-10 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-11 contextFilter:"main" splitStatements:false
ALTER TABLE User MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-12 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-13 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-14 contextFilter:"main" splitStatements:false
ALTER TABLE ac_cw_vm_map MODIFY CreatedDate datetime;

-- changeset redmi:1755871212748-15 contextFilter:"main" splitStatements:false
ALTER TABLE contract_signoff MODIFY DateSigned datetime;

-- changeset redmi:1755871212748-16 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY `Description` LONGTEXT;

-- changeset redmi:1755871212748-17 contextFilter:"main" splitStatements:false
ALTER TABLE User MODIFY Email VARCHAR(255) NULL;

-- changeset redmi:1755871212748-18 contextFilter:"main" splitStatements:false
ALTER TABLE User ALTER Email SET DEFAULT null;

-- changeset redmi:1755871212748-19 contextFilter:"main" splitStatements:false
ALTER TABLE Configuration ALTER IsActive SET DEFAULT 1;

-- changeset redmi:1755871212748-20 contextFilter:"main" splitStatements:false
ALTER TABLE User ALTER IsActive SET DEFAULT 1;

-- changeset redmi:1755871212748-21 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ALTER IsActive SET DEFAULT 1;

-- changeset redmi:1755871212748-22 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ALTER IsApproved SET DEFAULT 0;

-- changeset redmi:1755871212748-23 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER IsPartner SET DEFAULT 0;

-- changeset redmi:1755871212748-24 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY IsRestricted TINYINT(1);

-- changeset redmi:1755871212748-25 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY IsRestricted TINYINT(1) NULL;

-- changeset redmi:1755871212748-26 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER IsRestricted SET DEFAULT 0;

-- changeset redmi:1755871212748-27 contextFilter:"main" splitStatements:false
ALTER TABLE ConfigurationValues ALTER IsSecret SET DEFAULT 0;

-- changeset redmi:1755871212748-28 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ALTER IsVerified SET DEFAULT 0;

-- changeset redmi:1755871212748-29 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY Name LONGTEXT;

-- changeset redmi:1755871212748-30 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY Name LONGTEXT NULL;

-- changeset redmi:1755871212748-31 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER Name SET DEFAULT null;

-- changeset redmi:1755871212748-32 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY OrganizationId INT NULL;

-- changeset redmi:1755871212748-33 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER OrganizationId SET DEFAULT (NULL);

-- changeset redmi:1755871212748-34 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` MODIFY Permissions BLOB NULL;

-- changeset redmi:1755871212748-35 contextFilter:"main" splitStatements:false
ALTER TABLE `Role` ALTER Permissions SET DEFAULT (NULL);

-- changeset redmi:1755871212748-36 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY RoleId INT NULL;

-- changeset redmi:1755871212748-37 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ALTER RoleId SET DEFAULT (NULL);

-- changeset redmi:1755871212748-38 contextFilter:"main" splitStatements:false
ALTER TABLE ConfigurationValues MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-39 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-40 contextFilter:"main" splitStatements:false
ALTER TABLE User MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-41 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-42 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-43 contextFilter:"main" splitStatements:false
ALTER TABLE ac_cw_vm_map MODIFY UpdatedDate datetime;

-- changeset redmi:1755871212748-44 contextFilter:"main" splitStatements:false
ALTER TABLE ConfigurationValues MODIFY Value VARCHAR(65000);

-- changeset redmi:1755871212748-45 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_runlog_entry MODIFY createdAt timestamp;

-- changeset redmi:1755871212748-46 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_compute MODIFY endDate timestamp;

-- changeset redmi:1755871212748-47 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_ipaddress MODIFY endDate timestamp;

-- changeset redmi:1755871212748-48 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_networkbytes MODIFY endDate timestamp;

-- changeset redmi:1755871212748-49 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_primarystorage MODIFY endDate timestamp;

-- changeset redmi:1755871212748-50 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_secondarystorage MODIFY endDate timestamp;

-- changeset redmi:1755871212748-51 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_runlog MODIFY endTime timestamp;

-- changeset redmi:1755871212748-52 contextFilter:"main" splitStatements:false
ALTER TABLE ac_cw_vm_map ALTER isFormula SET DEFAULT 0;

-- changeset redmi:1755871212748-53 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_compute MODIFY startDate timestamp;

-- changeset redmi:1755871212748-54 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_ipaddress MODIFY startDate timestamp;

-- changeset redmi:1755871212748-55 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_networkbytes MODIFY startDate timestamp;

-- changeset redmi:1755871212748-56 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_primarystorage MODIFY startDate timestamp;

-- changeset redmi:1755871212748-57 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_secondarystorage MODIFY startDate timestamp;

-- changeset redmi:1755871212748-58 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_runlog MODIFY startTime timestamp;

-- liquibase formatted sql

-- changeset redmi:*************-1 contextFilter:"main" splitStatements:false
CREATE TABLE Configuration (ConfigurationId INT AUTO_INCREMENT NOT NULL, IsActive BIT DEFAULT 1 NOT NULL, Category VARCHAR(50) NOT NULL, CONSTRAINT PK_CONFIGURATION PRIMARY KEY (ConfigurationId));

-- changeset redmi:*************-2 contextFilter:"main" splitStatements:false
CREATE TABLE ConfigurationValues (ConfigurationValuesId INT AUTO_INCREMENT NOT NULL, Name VARCHAR(100) NOT NULL, Value VARCHAR(65000) NOT NULL, IsSecret BIT DEFAULT 0 NOT NULL, ConfigurationId INT NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, InputType VARCHAR(255) DEFAULT 'input' NOT NULL, CONSTRAINT PK_CONFIGURATIONVALUES PRIMARY KEY (ConfigurationValuesId));

-- changeset redmi:*************-3 contextFilter:"main" splitStatements:false
CREATE TABLE Image (ImageId INT AUTO_INCREMENT NOT NULL, ImageName VARCHAR(255) NOT NULL, ContentType VARCHAR(255) DEFAULT 'image/png' NOT NULL, ImageBinary LONGBLOB NOT NULL, CONSTRAINT PK_IMAGE PRIMARY KEY (ImageId));

-- changeset redmi:*************-4 contextFilter:"main" splitStatements:false
CREATE TABLE `Organization` (OrganizationId INT AUTO_INCREMENT NOT NULL, Name LONGTEXT NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, IsActive BIT NOT NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, AllowSubOrg BIT DEFAULT 0 NOT NULL, AllowWhiteLabel BIT DEFAULT 0 NOT NULL, ParentOrganizationId INT DEFAULT 0 NULL, IsVerified BIT DEFAULT 0 NOT NULL, IsPartner BIT DEFAULT 0 NOT NULL, CONSTRAINT PK_ORGANIZATION PRIMARY KEY (OrganizationId));

-- changeset redmi:*************-5 contextFilter:"main" splitStatements:false
CREATE TABLE OrganizationMapping (Id INT AUTO_INCREMENT NOT NULL, OrganizationId INT NOT NULL, Application ENUM('CloudInfra', 'ConnectWise') NOT NULL, PrimaryId VARCHAR(255) NOT NULL, PrimaryName VARCHAR(255) NULL, SecondaryId VARCHAR(255) NULL, SecondaryName VARCHAR(255) NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, CONSTRAINT PK_ORGANIZATIONMAPPING PRIMARY KEY (Id));

-- changeset redmi:*************-6 contextFilter:"main" splitStatements:false
CREATE TABLE `Role` (RoleId INT AUTO_INCREMENT NOT NULL, Name LONGTEXT NULL, OrganizationId INT DEFAULT NULL NULL, Permissions BINARY(20) DEFAULT NULL NULL, AvailableTo INT DEFAULT NULL NULL, IsRestricted BIT DEFAULT 0 NULL, `Description` LONGTEXT NULL, CONSTRAINT PK_ROLE PRIMARY KEY (RoleId));

-- changeset redmi:*************-7 contextFilter:"main" splitStatements:false
CREATE TABLE User (UserId INT AUTO_INCREMENT NOT NULL, FirstName VARCHAR(255) NULL, LastName VARCHAR(255) NULL, Email VARCHAR(255) NULL, Password VARCHAR(255) NULL, IsActive BIT DEFAULT 1 NOT NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, CONSTRAINT PK_USER PRIMARY KEY (UserId));

-- changeset redmi:*************-8 contextFilter:"main" splitStatements:false
CREATE TABLE User_Organization_Mapping (MappingId INT AUTO_INCREMENT NOT NULL, UserId INT DEFAULT NULL NULL, OrganizationId INT DEFAULT NULL NULL, RoleId INT DEFAULT NULL NULL, CreatedBy INT DEFAULT NULL NULL, CreatedDate datetime DEFAULT '0001-01-01 00:00:00' NOT NULL, IsActive BIT DEFAULT 0 NOT NULL, UpdatedBy INT DEFAULT NULL NULL, UpdatedDate datetime DEFAULT NULL NULL, IsDefault BIT DEFAULT 0 NOT NULL, IsApproved BIT DEFAULT 0 NOT NULL, CONSTRAINT PK_USER_ORGANIZATION_MAPPING PRIMARY KEY (MappingId));

-- changeset redmi:*************-9 contextFilter:"main" splitStatements:false
CREATE TABLE WhiteLabel (WhiteLabelId INT AUTO_INCREMENT NOT NULL, OrganizationId INT NOT NULL, PortalName VARCHAR(255) NULL, DomainName VARCHAR(255) NULL, PrimaryColor VARCHAR(20) NULL, SecondaryColor VARCHAR(20) NULL, LoginLogo INT DEFAULT NULL NULL, MainPortalLogo INT DEFAULT NULL NULL, BannerLogo INT DEFAULT NULL NULL, NavigationLogo INT DEFAULT NULL NULL, FavIcon INT DEFAULT NULL NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, IsActive BIT DEFAULT 1 NULL, AdaptiveCloudHostname VARCHAR(255) NULL, CONSTRAINT PK_WHITELABEL PRIMARY KEY (WhiteLabelId));

-- changeset redmi:*************-10 contextFilter:"main" splitStatements:false
CREATE TABLE ac_cw_account_map (id INT AUTO_INCREMENT NOT NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NULL, cwCompanyId INT DEFAULT NULL NULL, cwCompanyName VARCHAR(255) NULL, cwCompanyIdentifier VARCHAR(80) NULL, cwAgreementId INT DEFAULT NULL NULL, cwAgreementName VARCHAR(80) NULL, enabled TINYINT DEFAULT 0 NOT NULL, CreatedBy INT DEFAULT NULL NULL, UpdatedBy INT DEFAULT NULL NULL, acType VARCHAR(20) DEFAULT 'Account' NOT NULL, billingStartDate date DEFAULT NULL NULL, CONSTRAINT PK_AC_CW_ACCOUNT_MAP PRIMARY KEY (id), UNIQUE (acId));

-- changeset redmi:*************-11 contextFilter:"main" splitStatements:false
CREATE TABLE ac_cw_product_map (id INT AUTO_INCREMENT NOT NULL, usageType VARCHAR(20) NULL, cwproductid INT DEFAULT NULL NULL, cwproductname VARCHAR(40) NULL, label VARCHAR(40) NULL, units VARCHAR(255) NULL, `description` VARCHAR(255) NULL, prorate TINYINT DEFAULT 0 NULL, bandwidthgb INT DEFAULT NULL NULL, tiered TINYINT DEFAULT 0 NULL, valuefn VARCHAR(80) NULL, CONSTRAINT PK_AC_CW_PRODUCT_MAP PRIMARY KEY (id));

-- changeset redmi:*************-12 contextFilter:"main" splitStatements:false
CREATE TABLE ac_cw_vm_map (id INT AUTO_INCREMENT NOT NULL, acId VARCHAR(36) NULL, acName VARCHAR(255) NULL, acType VARCHAR(20) NULL, productMapId INT NOT NULL, priority INT DEFAULT NULL NULL, quantityValue VARCHAR(255) NULL, isFormula BIT DEFAULT 0 NOT NULL, domain VARCHAR(80) NULL, account VARCHAR(80) NULL, startDate date NOT NULL, endDate date DEFAULT NULL NULL, CreatedBy INT NOT NULL, UpdatedBy INT DEFAULT NULL NULL, CreatedDate datetime NOT NULL, UpdatedDate datetime DEFAULT NULL NULL, CONSTRAINT PK_AC_CW_VM_MAP PRIMARY KEY (id));

-- changeset redmi:*************-13 contextFilter:"main" splitStatements:false
CREATE TABLE acusage (Id INT AUTO_INCREMENT NOT NULL, acId VARCHAR(36) NULL, label VARCHAR(40) NULL, usageType VARCHAR(80) NULL, productMapId INT NOT NULL, actualUsage DOUBLE DEFAULT NULL NULL, quantity DOUBLE DEFAULT NULL NULL, unitPrice DOUBLE DEFAULT NULL NULL, month date DEFAULT NULL NULL, CONSTRAINT PK_ACUSAGE PRIMARY KEY (Id));

-- changeset redmi:*************-14 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_compute (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, totalvcpus DOUBLE NOT NULL, totalramgb DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_COMPUTE PRIMARY KEY (Id));

-- changeset redmi:*************-15 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_compute_rec (Id INT AUTO_INCREMENT NOT NULL, computeId INT NOT NULL, vmid VARCHAR(36) NULL, name VARCHAR(255) NULL, ostypeid INT DEFAULT NULL NULL, templateid VARCHAR(36) NULL, vcpus DOUBLE DEFAULT NULL NULL, ramgb DOUBLE DEFAULT NULL NULL, hoursused DOUBLE DEFAULT NULL NULL, avgvcpus DOUBLE DEFAULT NULL NULL, avgramgb DOUBLE DEFAULT NULL NULL, startDate date DEFAULT NULL NULL, endDate date DEFAULT NULL NULL, month date DEFAULT NULL NULL, CONSTRAINT PK_ACUSAGE_COMPUTE_REC PRIMARY KEY (Id));

-- changeset redmi:*************-16 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_ipaddress (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, totalhours DOUBLE NOT NULL, averageips DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_IPADDRESS PRIMARY KEY (Id));

-- changeset redmi:*************-17 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_license (Id INT AUTO_INCREMENT NOT NULL, computeRecId INT NOT NULL, productMapId INT DEFAULT NULL NULL, quantity DOUBLE DEFAULT NULL NULL, CONSTRAINT PK_ACUSAGE_LICENSE PRIMARY KEY (Id));

-- changeset redmi:*************-18 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_networkbytes (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, networkgb DOUBLE NOT NULL, networkb DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_NETWORKBYTES PRIMARY KEY (Id));

-- changeset redmi:*************-19 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_primarystorage (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, gbused DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, HWMDay date NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_PRIMARYSTORAGE PRIMARY KEY (Id));

-- changeset redmi:*************-20 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_runlog (Id INT AUTO_INCREMENT NOT NULL, command VARCHAR(20) NOT NULL, startTime timestamp DEFAULT NOW() NOT NULL, endTime timestamp DEFAULT NULL NULL, month date NOT NULL, errorCount INT DEFAULT 0 NOT NULL, CONSTRAINT PK_ACUSAGE_RUNLOG PRIMARY KEY (Id));

-- changeset redmi:*************-21 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_runlog_entry (Id INT AUTO_INCREMENT NOT NULL, runlogId INT NOT NULL, level INT NOT NULL, levelName VARCHAR(20) NOT NULL, createdAt timestamp DEFAULT NOW() NOT NULL, msg VARCHAR(255) NOT NULL, CONSTRAINT PK_ACUSAGE_RUNLOG_ENTRY PRIMARY KEY (Id));

-- changeset redmi:*************-22 contextFilter:"main" splitStatements:false
CREATE TABLE acusage_secondarystorage (Id INT AUTO_INCREMENT NOT NULL, domainId VARCHAR(36) NULL, acId VARCHAR(36) NOT NULL, acName VARCHAR(255) NOT NULL, gbused DOUBLE NOT NULL, startDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, endDate timestamp DEFAULT '0000-00-00 00:00:00' NOT NULL, HWMDay date NOT NULL, month date NOT NULL, CONSTRAINT PK_ACUSAGE_SECONDARYSTORAGE PRIMARY KEY (Id));

-- changeset redmi:*************-23 contextFilter:"main" splitStatements:false
CREATE TABLE contract_signoff (Id INT AUTO_INCREMENT NOT NULL, OrganizationId INT NOT NULL, UserId INT NOT NULL, DocumentId INT NOT NULL, DateSigned datetime NOT NULL, IPAddress VARCHAR(100) NOT NULL, CONSTRAINT PK_CONTRACT_SIGNOFF PRIMARY KEY (Id));

-- changeset redmi:*************-24 contextFilter:"main" splitStatements:false
CREATE TABLE device_folder (FolderId INT AUTO_INCREMENT NOT NULL, ParentFolderId INT DEFAULT NULL NULL, OrganizationId INT NOT NULL, Name VARCHAR(50) NOT NULL, SortId SMALLINT UNSIGNED DEFAULT 0 NOT NULL, CONSTRAINT PK_DEVICE_FOLDER PRIMARY KEY (FolderId));

-- changeset redmi:*************-25 contextFilter:"main" splitStatements:false
CREATE TABLE role_available_to (RoleAvailableToId INT AUTO_INCREMENT NOT NULL, Name LONGTEXT NULL, `Description` LONGTEXT NULL, CONSTRAINT PK_ROLE_AVAILABLE_TO PRIMARY KEY (RoleAvailableToId));

-- changeset redmi:*************-26 contextFilter:"main" splitStatements:false
CREATE TABLE term_documents (Id INT AUTO_INCREMENT NOT NULL, Name VARCHAR(100) NOT NULL, Type INT NOT NULL, Version INT NOT NULL, Content LONGBLOB NOT NULL, CONSTRAINT PK_TERM_DOCUMENTS PRIMARY KEY (Id));

-- changeset redmi:*************-27 contextFilter:"main" splitStatements:false
ALTER TABLE contract_signoff ADD CONSTRAINT OrganizationId_UserId_DocumentId UNIQUE (OrganizationId, UserId, DocumentId);

-- changeset redmi:*************-28 contextFilter:"main" splitStatements:false
ALTER TABLE term_documents ADD CONSTRAINT Type_Version UNIQUE (Type, Version);

-- changeset redmi:*************-29 contextFilter:"main" splitStatements:false
ALTER TABLE acusage ADD CONSTRAINT acId_month_product UNIQUE (acId, month, productMapId);

-- changeset redmi:*************-30 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_license ADD CONSTRAINT computeRecId UNIQUE (computeRecId, productMapId);

-- changeset redmi:*************-31 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_compute ADD CONSTRAINT ix_compute_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-32 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_ipaddress ADD CONSTRAINT ix_ipaddress_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-33 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_networkbytes ADD CONSTRAINT ix_networkbytes_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-34 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_primarystorage ADD CONSTRAINT ix_primarystorage_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-35 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_secondarystorage ADD CONSTRAINT ix_secondarystorage_acId_month UNIQUE (acId, month);

-- changeset redmi:*************-36 contextFilter:"main" splitStatements:false
CREATE INDEX FK__term_documents ON contract_signoff(DocumentId);

-- changeset redmi:*************-37 contextFilter:"main" splitStatements:false
CREATE INDEX FK__user ON contract_signoff(UserId);

-- changeset redmi:*************-38 contextFilter:"main" splitStatements:false
CREATE INDEX FK_device_folder_device_folder ON device_folder(ParentFolderId);

-- changeset redmi:*************-39 contextFilter:"main" splitStatements:false
CREATE INDEX FK_organization ON device_folder(OrganizationId);

-- changeset redmi:*************-40 contextFilter:"main" splitStatements:false
CREATE INDEX IX_ConfigurationValues_ConfigurationId ON ConfigurationValues(ConfigurationId);

-- changeset redmi:*************-41 contextFilter:"main" splitStatements:false
CREATE INDEX IX_Organization_ParentOrganizationId ON `Organization`(ParentOrganizationId);

-- changeset redmi:*************-42 contextFilter:"main" splitStatements:false
CREATE INDEX IX_User_Organization_Mapping_OrganizationId ON User_Organization_Mapping(OrganizationId);

-- changeset redmi:*************-43 contextFilter:"main" splitStatements:false
CREATE INDEX IX_User_Organization_Mapping_RoleId ON User_Organization_Mapping(RoleId);

-- changeset redmi:*************-44 contextFilter:"main" splitStatements:false
CREATE INDEX IX_User_Organization_Mapping_UserId ON User_Organization_Mapping(UserId);

-- changeset redmi:*************-45 contextFilter:"main" splitStatements:false
CREATE INDEX IX_WhiteLabel_BannerLogo ON WhiteLabel(BannerLogo);

-- changeset redmi:*************-46 contextFilter:"main" splitStatements:false
CREATE INDEX IX_WhiteLabel_FavIcon ON WhiteLabel(FavIcon);

-- changeset redmi:*************-47 contextFilter:"main" splitStatements:false
CREATE INDEX IX_WhiteLabel_LoginLogo ON WhiteLabel(LoginLogo);

-- changeset redmi:*************-48 contextFilter:"main" splitStatements:false
CREATE INDEX IX_WhiteLabel_MainPortalLogo ON WhiteLabel(MainPortalLogo);

-- changeset redmi:*************-49 contextFilter:"main" splitStatements:false
CREATE INDEX IX_WhiteLabel_NavigationLogo ON WhiteLabel(NavigationLogo);

-- changeset redmi:*************-50 contextFilter:"main" splitStatements:false
CREATE INDEX IX_WhiteLabel_OrganizationId ON WhiteLabel(OrganizationId);

-- changeset redmi:*************-51 contextFilter:"main" splitStatements:false
CREATE INDEX computeId ON acusage_compute_rec(computeId);

-- changeset redmi:*************-52 contextFilter:"main" splitStatements:false
CREATE INDEX fkRunlogId ON acusage_runlog_entry(runlogId);

-- changeset redmi:*************-53 contextFilter:"main" splitStatements:false
CREATE INDEX productMapId ON ac_cw_vm_map(productMapId);

-- changeset redmi:*************-54 contextFilter:"main" splitStatements:false
CREATE INDEX productMapId ON acusage(productMapId);

-- changeset redmi:*************-55 contextFilter:"main" splitStatements:false
ALTER TABLE ConfigurationValues ADD CONSTRAINT FK_ConfigurationValues_Configuration_ConfigurationId FOREIGN KEY (ConfigurationId) REFERENCES Configuration (ConfigurationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-56 contextFilter:"main" splitStatements:false
ALTER TABLE `Organization` ADD CONSTRAINT FK_Organization_Organization_ParentOrganizationId FOREIGN KEY (ParentOrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-57 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_Organization_OrganizationId FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-58 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_Role_RoleId FOREIGN KEY (RoleId) REFERENCES `Role` (RoleId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-59 contextFilter:"main" splitStatements:false
ALTER TABLE User_Organization_Mapping ADD CONSTRAINT FK_User_Organization_Mapping_User_UserId FOREIGN KEY (UserId) REFERENCES User (UserId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-60 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_BannerLogo FOREIGN KEY (BannerLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-61 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_FavIcon FOREIGN KEY (FavIcon) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-62 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_LoginLogo FOREIGN KEY (LoginLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-63 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_MainPortalLogo FOREIGN KEY (MainPortalLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-64 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Image_NavigationLogo FOREIGN KEY (NavigationLogo) REFERENCES Image (ImageId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-65 contextFilter:"main" splitStatements:false
ALTER TABLE WhiteLabel ADD CONSTRAINT FK_WhiteLabel_Organization_OrganizationId FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-66 contextFilter:"main" splitStatements:false
ALTER TABLE contract_signoff ADD CONSTRAINT FK__organization FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE NO ACTION ON DELETE NO ACTION;

-- changeset redmi:*************-67 contextFilter:"main" splitStatements:false
ALTER TABLE contract_signoff ADD CONSTRAINT FK__term_documents FOREIGN KEY (DocumentId) REFERENCES term_documents (Id) ON UPDATE NO ACTION ON DELETE NO ACTION;

-- changeset redmi:*************-68 contextFilter:"main" splitStatements:false
ALTER TABLE contract_signoff ADD CONSTRAINT FK__user FOREIGN KEY (UserId) REFERENCES User (UserId) ON UPDATE NO ACTION ON DELETE NO ACTION;

-- changeset redmi:*************-69 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder ADD CONSTRAINT FK_device_folder_device_folder FOREIGN KEY (ParentFolderId) REFERENCES device_folder (FolderId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-70 contextFilter:"main" splitStatements:false
ALTER TABLE device_folder ADD CONSTRAINT FK_organization FOREIGN KEY (OrganizationId) REFERENCES `Organization` (OrganizationId) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-71 contextFilter:"main" splitStatements:false
ALTER TABLE ac_cw_vm_map ADD CONSTRAINT ac_cw_vm_map_ibfk_1 FOREIGN KEY (productMapId) REFERENCES ac_cw_product_map (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-72 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_compute_rec ADD CONSTRAINT acusage_compute_rec_ibfk_1 FOREIGN KEY (computeId) REFERENCES acusage_compute (Id) ON UPDATE RESTRICT ON DELETE CASCADE;

-- changeset redmi:*************-73 contextFilter:"main" splitStatements:false
ALTER TABLE acusage ADD CONSTRAINT acusage_ibfk_1 FOREIGN KEY (productMapId) REFERENCES ac_cw_product_map (id) ON UPDATE RESTRICT ON DELETE RESTRICT;

-- changeset redmi:*************-74 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_license ADD CONSTRAINT acusage_license_ibfk_1 FOREIGN KEY (computeRecId) REFERENCES acusage_compute_rec (Id) ON UPDATE RESTRICT ON DELETE CASCADE;

-- changeset redmi:*************-75 contextFilter:"main" splitStatements:false
ALTER TABLE acusage_runlog_entry ADD CONSTRAINT fkRunlogId FOREIGN KEY (runlogId) REFERENCES acusage_runlog (Id) ON UPDATE RESTRICT ON DELETE CASCADE;

